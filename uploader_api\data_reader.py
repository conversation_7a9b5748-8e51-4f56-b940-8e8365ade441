import pandas as pd
import csv

# === CONFIG ===
SHOP_ID = "0706/6071/8813"  # <<< REPLACE with your real shop ID
data_file = "data.xlsx"  # Input data file
template_file = "products_export.csv"  # Template CSV file
output_file = "processed_data.csv"  # Output file (simple CSV)
shopify_output_file = "shopify_export_nose_straight.csv"  # Shopify-formatted output
bodyHeader = "Nose Straight 14K"

# Column headers
styleHeader = "style_no"
gramHeader = "Gram"
colorHeader = "color"  

def format_handle(style):
    """Format style number for Shopify handle"""
    return style.strip().lower().replace(" ", "-")

def generate_image_url(style_number):
    """Generate image URL for Shopify"""
    # Clean the style number: remove 'f' prefix, convert parentheses and spaces to underscores
    cleaned_style = style_number.strip()

    # Remove 'f' prefix if present
    if cleaned_style.lower().startswith('f'):
        cleaned_style = cleaned_style[1:]

    # Replace parentheses and their contents, and spaces with underscores
    import re
    # Replace (content) with _CONTENT and remove parentheses
    cleaned_style = re.sub(r'\(([^)]+)\)', r'_\1', cleaned_style)
    # Replace spaces with underscores
    cleaned_style = cleaned_style.replace(' ', '')
    # Convert to uppercase
    cleaned_style = cleaned_style.upper()

    return f"https://cdn.shopify.com/s/files/1/{SHOP_ID}/files/{cleaned_style}.png"

def read_and_process_data(input_file, output_file):
    """
    Read style number, gram, and color data from Excel file.
    Apply default values and filtering rules:
    - Color: Fill "Yellow" if missing
    - Gram: Fill "N/A" if missing  
    - Skip rows where Style Number is None/empty
    """
    
    try:
        # Read Excel file
        print(f"📖 Reading data from: {input_file}")
        df = pd.read_excel(input_file, dtype=str)
        
        # Replace NaN with empty strings for easier processing
        df = df.fillna("")
        
        print(f"📊 Total rows in input file: {len(df)}")
        
        # Process data
        processed_rows = []
        skipped_count = 0
        
        for index, row in df.iterrows():
            # Get style number and check if it's valid
            style = row.get(styleHeader, "").strip()
            
            # Skip rows where style number is None, empty, or "None"
            if not style or style.lower() == "none":
                skipped_count += 1
                print(f"⏭️  Skipping row {index + 1}: Style Number is empty or None")
                continue
            
            # Get gram value, fill "N/A" if missing
            gram = row.get(gramHeader, "").strip()
            if not gram or gram.lower() == "none":
                gram = "N/A"
            
            # Get color value, fill "Yellow" if missing
            color = row.get(colorHeader, "").strip()
            if not color or color.lower() == "none":
                color = "Yellow"
            
            # Create processed row
            processed_row = {
                "Style Number": style,
                "Gram": gram,
                "Color": color
            }
            
            processed_rows.append(processed_row)
            print(f"✅ Processed row {index + 1}: Style={style}, Gram={gram}, Color={color}")
        
        # Write to CSV
        if processed_rows:
            print(f"\n💾 Writing {len(processed_rows)} processed rows to: {output_file}")
            
            with open(output_file, mode='w', newline='', encoding='utf-8') as f:
                fieldnames = ["Style Number", "Gram", "Color"]
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(processed_rows)
            
            print(f"✅ Successfully processed {len(processed_rows)} rows")
            print(f"⏭️  Skipped {skipped_count} rows (empty/None style numbers)")
            print(f"📄 Output saved to: {output_file}")
        else:
            print("❌ No valid rows to process!")
            
    except FileNotFoundError:
        print(f"❌ Error: File '{input_file}' not found!")
    except Exception as e:
        print(f"❌ Error processing file: {str(e)}")

def create_shopify_export_from_template(input_file, template_file, shopify_output_file):
    """
    Create Shopify-formatted export using template and processed data.
    Uses the same logic as test3.py but with updated requirements.
    """
    try:
        # Read template CSV (only first row for default values)
        print(f"📖 Reading template from: {template_file}")
        with open(template_file, mode='r', encoding='utf-8-sig') as f:
            reader = csv.DictReader(f)
            default_row = next(reader)
            fieldnames = reader.fieldnames

        # Read data Excel
        print(f"📖 Reading data from: {input_file}")
        df = pd.read_excel(input_file, dtype=str).fillna("")

        print(f"📊 Total rows in input file: {len(df)}")

        # Prepare new rows
        new_rows = []
        skipped_count = 0

        for index, row in df.iterrows():
            # Get style number and check if it's valid
            style = row.get(styleHeader, "").strip()

            # Skip rows where style number is None, empty, or "None"
            if not style or style.lower() == "none":
                skipped_count += 1
                print(f"⏭️  Skipping row {index + 1}: Style Number is empty or None")
                continue

            # Get gram value, fill "N/A" if missing
            gram = row.get(gramHeader, "").strip()
            if not gram or gram.lower() == "none":
                gram = "N/A"

            # Get color value, fill "Yellow" if missing
            color = row.get(colorHeader, "").strip()
            if not color or color.lower() == "none":
                color = "Yellow"

            # Clone default template row and override fields
            new_row = default_row.copy()
            new_row["Handle"] = format_handle(style)
            new_row["Title"] = style
            new_row["Body (HTML)"] = bodyHeader
            new_row["Variant SKU"] = style
            new_row["Option2 Value"] = gram
            new_row["Option3 Value"] = color
            new_row["Image Src"] = generate_image_url(style)
            new_row["Color (product.metafields.shopify.color-pattern)"] = color

            new_rows.append(new_row)
            print(f"✅ Processed row {index + 1}: Style={style}, Gram={gram}, Color={color}")

        # Write to Shopify export file
        if new_rows:
            print(f"\n💾 Writing {len(new_rows)} Shopify rows to: {shopify_output_file}")

            with open(shopify_output_file, mode='w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(new_rows)

            print(f"✅ Successfully created Shopify export with {len(new_rows)} products")
            print(f"⏭️  Skipped {skipped_count} rows (empty/None style numbers)")
            print(f"📄 Shopify export saved to: {shopify_output_file}")
        else:
            print("❌ No valid rows to process for Shopify export!")

    except FileNotFoundError as e:
        print(f"❌ Error: File not found - {str(e)}")
    except Exception as e:
        print(f"❌ Error creating Shopify export: {str(e)}")

def display_summary(output_file):
    """Display a summary of the processed data"""
    try:
        df = pd.read_csv(output_file)
        print(f"\n📊 SUMMARY OF PROCESSED DATA:")
        print(f"Total processed records: {len(df)}")
        print(f"Records with 'N/A' gram: {len(df[df['Gram'] == 'N/A'])}")
        print(f"Records with 'Yellow' color: {len(df[df['Color'] == 'Yellow'])}")
        print(f"Unique colors: {df['Color'].unique()}")
        print(f"Unique grams: {df['Gram'].unique()}")
        
        # Show first few rows
        print(f"\n📋 First 5 processed records:")
        print(df.head().to_string(index=False))
        
    except FileNotFoundError:
        print(f"❌ Output file '{output_file}' not found for summary!")
    except Exception as e:
        print(f"❌ Error displaying summary: {str(e)}")

# === Main execution ===
if __name__ == "__main__":
    print("🚀 Starting data processing...")

    # Option 1: Process data to simple CSV
    print("\n📋 OPTION 1: Creating simple processed CSV...")
    read_and_process_data(data_file, output_file)
    display_summary(output_file)

    # Option 2: Create Shopify export using template
    print("\n🛍️  OPTION 2: Creating Shopify export from template...")
    create_shopify_export_from_template(data_file, template_file, shopify_output_file)

    print("\n🎉 All processing complete!")
    print(f"📄 Simple CSV: {output_file}")
    print(f"🛍️  Shopify Export: {shopify_output_file}")
